import { describe, it, expect, vi, beforeEach } from "vitest";

import {
  getFundsAction,
  searchFundsAction,
  validateFundAction,
} from "@/lib/actions";
import { getFunds, searchFunds, validateFund } from "@/lib/mockData";

// Mock the mockData module
vi.mock("@/lib/mockData");

const mockFunds = [
  {
    id: "fund_001",
    name: "测试股票基金",
    code: "000001",
    type: "stock",
    description: "这是一个测试股票基金",
  },
  {
    id: "fund_002",
    name: "测试债券基金",
    code: "000002",
    type: "bond",
    description: "这是一个测试债券基金",
  },
];

describe("Server Actions", () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mocks
    (getFunds as any).mockResolvedValue(mockFunds);
    (searchFunds as any).mockResolvedValue(mockFunds.slice(0, 1));
    (validateFund as any).mockResolvedValue(true);
  });

  describe("getFundsAction", () => {
    it("应该成功获取所有基金", async () => {
      const result = await getFundsAction();

      expect(getFunds).toHaveBeenCalled();
      expect(result).toEqual(mockFunds);
    });

    it("应该处理获取基金失败的情况", async () => {
      const errorMessage = "Database connection failed";
      (getFunds as any).mockRejectedValue(new Error(errorMessage));

      await expect(getFundsAction()).rejects.toThrow("Failed to load funds");
      expect(getFunds).toHaveBeenCalled();
    });

    it("应该记录错误日志", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      const originalError = new Error("Test error");
      (getFunds as any).mockRejectedValue(originalError);

      try {
        await getFundsAction();
      } catch {
        // Expected to throw
      }

      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to load funds:",
        originalError
      );
      consoleSpy.mockRestore();
    });

    it("应该返回正确的数据类型", async () => {
      const result = await getFundsAction();

      expect(Array.isArray(result)).toBe(true);
      for (const fund of result) {
        expect(fund).toHaveProperty("id");
        expect(fund).toHaveProperty("name");
        expect(fund).toHaveProperty("code");
        expect(fund).toHaveProperty("type");
      }
    });
  });

  describe("searchFundsAction", () => {
    it("应该成功搜索基金", async () => {
      const query = "股票";
      const result = await searchFundsAction(query);

      expect(searchFunds).toHaveBeenCalledWith(query);
      expect(result).toEqual(mockFunds.slice(0, 1));
    });

    it("应该处理空查询字符串", async () => {
      const result = await searchFundsAction("");

      expect(searchFunds).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it("应该处理只包含空格的查询字符串", async () => {
      const result = await searchFundsAction("   ");

      expect(searchFunds).not.toHaveBeenCalled();
      expect(result).toEqual([]);
    });

    it("应该处理搜索失败的情况", async () => {
      const errorMessage = "Search service unavailable";
      (searchFunds as any).mockRejectedValue(new Error(errorMessage));

      await expect(searchFundsAction("test")).rejects.toThrow(
        "Failed to search funds"
      );
      expect(searchFunds).toHaveBeenCalledWith("test");
    });

    it("应该记录搜索错误日志", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      const originalError = new Error("Search error");
      (searchFunds as any).mockRejectedValue(originalError);

      try {
        await searchFundsAction("test");
      } catch {
        // Expected to throw
      }

      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to search funds:",
        originalError
      );
      consoleSpy.mockRestore();
    });

    it("应该处理特殊字符查询", async () => {
      const specialQuery = "基金@#$%";
      await searchFundsAction(specialQuery);

      expect(searchFunds).toHaveBeenCalledWith(specialQuery);
    });

    it("应该处理数字查询", async () => {
      const numericQuery = "000001";
      await searchFundsAction(numericQuery);

      expect(searchFunds).toHaveBeenCalledWith(numericQuery);
    });

    it("应该返回正确的数据类型", async () => {
      const result = await searchFundsAction("test");

      expect(Array.isArray(result)).toBe(true);
      for (const fund of result) {
        expect(fund).toHaveProperty("id");
        expect(fund).toHaveProperty("name");
        expect(fund).toHaveProperty("code");
        expect(fund).toHaveProperty("type");
      }
    });
  });

  describe("validateFundAction", () => {
    it("应该验证有效的基金代码", async () => {
      const result = await validateFundAction("000001");

      expect(validateFund).toHaveBeenCalledWith("000001");
      expect(result).toBe(true);
    });

    it("应该拒绝无效格式的基金代码", async () => {
      const invalidCodes = [
        "12345", // 5位数字
        "1234567", // 7位数字
        "abc123", // 包含字母
        "000-01", // 包含特殊字符
        "", // 空字符串
        "000 01", // 包含空格
      ];

      for (const code of invalidCodes) {
        const result = await validateFundAction(code);
        expect(result).toBe(false);
        expect(validateFund).not.toHaveBeenCalledWith(code);
      }
    });

    it("应该处理验证服务失败的情况", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      (validateFund as any).mockRejectedValue(
        new Error("Validation service error")
      );

      const result = await validateFundAction("000001");

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to validate fund:",
        expect.any(Error)
      );
      consoleSpy.mockRestore();
    });

    it("应该处理验证返回false的情况", async () => {
      (validateFund as any).mockResolvedValue(false);

      const result = await validateFundAction("000001");

      expect(validateFund).toHaveBeenCalledWith("000001");
      expect(result).toBe(false);
    });

    it("应该验证边界情况的基金代码", async () => {
      const boundaryCodes = [
        "000000", // 最小值
        "999999", // 最大值
      ];

      for (const code of boundaryCodes) {
        await validateFundAction(code);
        expect(validateFund).toHaveBeenCalledWith(code);
      }
    });

    it("应该处理null和undefined输入", async () => {
      const result1 = await validateFundAction(null as any);
      const result2 = await validateFundAction(undefined as any);

      expect(result1).toBe(false);
      expect(result2).toBe(false);
      expect(validateFund).not.toHaveBeenCalled();
    });

    it("应该处理数字类型输入", async () => {
      const result = await validateFundAction(123_456 as any);

      expect(result).toBe(false);
      expect(validateFund).not.toHaveBeenCalled();
    });

    it("应该记录验证错误但不抛出异常", async () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});
      (validateFund as any).mockRejectedValue(new Error("Network error"));

      const result = await validateFundAction("000001");

      expect(result).toBe(false);
      expect(consoleSpy).toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe("错误处理", () => {
    it("所有action都应该有适当的错误处理", async () => {
      const actions = [
        () => getFundsAction(),
        () => searchFundsAction("test"),
        () => validateFundAction("000001"),
      ];

      // Mock all dependencies to throw errors
      (getFunds as any).mockRejectedValue(new Error("Test error"));
      (searchFunds as any).mockRejectedValue(new Error("Test error"));
      (validateFund as any).mockRejectedValue(new Error("Test error"));

      for (const action of actions) {
        try {
          await action();
        } catch {
          // getFundsAction and searchFundsAction should throw
          // validateFundAction should return false
        }
      }

      // validateFundAction should not throw, just return false
      const validateResult = await validateFundAction("000001");
      expect(validateResult).toBe(false);
    });
  });

  describe("并发处理", () => {
    it("应该能够处理并发请求", async () => {
      const promises = [
        getFundsAction(),
        searchFundsAction("test1"),
        searchFundsAction("test2"),
        validateFundAction("000001"),
        validateFundAction("000002"),
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(5);
      expect(Array.isArray(results[0])).toBe(true); // getFundsAction result
      expect(Array.isArray(results[1])).toBe(true); // searchFundsAction result
      expect(Array.isArray(results[2])).toBe(true); // searchFundsAction result
      expect(typeof results[3]).toBe("boolean"); // validateFundAction result
      expect(typeof results[4]).toBe("boolean"); // validateFundAction result
    });
  });
});
