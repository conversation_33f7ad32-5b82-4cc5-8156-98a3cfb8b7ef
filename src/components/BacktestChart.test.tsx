import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi } from "vitest";

import type { BacktestResult } from "@/types/fund";

import BacktestChart from "./BacktestChart";

// Mock recharts components
vi.mock("recharts", () => ({
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  LineChart: ({ children }: any) => (
    <div data-testid="line-chart">{children}</div>
  ),
  Line: ({ dataKey }: any) => <div data-testid={`line-${dataKey}`} />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ReferenceLine: ({ y }: any) => <div data-testid={`reference-line-${y}`} />,
}));

const mockBacktestResult: BacktestResult = {
  strategy: "fixed_amount",
  fund: {
    id: "test-fund",
    name: "测试基金",
    code: "000001",
    type: "stock",
    riskLevel: "medium",
    indexId: "test-index",
  },
  params: {
    startDate: "2024-01-01",
    endDate: "2024-12-31",
    initialAmount: 10_000,
    movingAveragePeriod: 20,
    deviationThreshold: 5,
    rebalanceFrequency: "weekly",
  },
  performance: {
    totalReturn: 15.5,
    annualizedReturn: 15.5,
    volatility: 12.3,
    sharpeRatio: 1.26,
    maxDrawdown: 8.2,
    totalInvestment: 10_000,
    finalValue: 11_550,
  },
  timeline: [
    {
      date: "2024-01-01",
      investment: 1000,
      shares: 1000,
      value: 1000,
      totalInvestment: 1000,
      netAssetValue: 1,
      return: 0,
    },
    {
      date: "2024-02-01",
      investment: 1000,
      shares: 909.09,
      value: 2100,
      totalInvestment: 2000,
      netAssetValue: 1.1,
      return: 5,
    },
  ],
};

describe("BacktestChart", () => {
  it("应该渲染图表组件", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByTestId("responsive-container")).toBeInTheDocument();
    expect(screen.getByTestId("line-chart")).toBeInTheDocument();
  });

  it("应该显示正确的图表元素", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByTestId("x-axis")).toBeInTheDocument();
    expect(screen.getByTestId("y-axis")).toBeInTheDocument();
    expect(screen.getByTestId("cartesian-grid")).toBeInTheDocument();
    expect(screen.getByTestId("tooltip")).toBeInTheDocument();
    expect(screen.getByTestId("legend")).toBeInTheDocument();
  });

  it("应该渲染数据线", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByTestId("line-value")).toBeInTheDocument();
    expect(screen.getByTestId("line-investment")).toBeInTheDocument();
  });

  it("应该显示零轴参考线", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByTestId("reference-line-0")).toBeInTheDocument();
  });

  it("应该处理空数据", () => {
    const emptyResult = {
      ...mockBacktestResult,
      timeline: [],
    };

    render(<BacktestChart result={emptyResult} />);

    expect(screen.getByText("回测结果")).toBeInTheDocument();
  });

  it("应该显示性能指标", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByText("总收益率")).toBeInTheDocument();
    expect(screen.getByText("15.50%")).toBeInTheDocument();
    expect(screen.getByText("年化收益率")).toBeInTheDocument();
    expect(screen.getByText("波动率")).toBeInTheDocument();
    expect(screen.getByText("12.30%")).toBeInTheDocument();
  });

  it("应该显示夏普比率和最大回撤", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByText("夏普比率")).toBeInTheDocument();
    expect(screen.getByText("1.26")).toBeInTheDocument();
    expect(screen.getByText("最大回撤")).toBeInTheDocument();
    expect(screen.getByText("8.20%")).toBeInTheDocument();
  });

  it("应该显示投资总额和最终价值", () => {
    render(<BacktestChart result={mockBacktestResult} />);

    expect(screen.getByText("投资总额")).toBeInTheDocument();
    expect(screen.getByText("最终价值")).toBeInTheDocument();
  });

  it("应该处理负收益率", () => {
    const negativeResult = {
      ...mockBacktestResult,
      performance: {
        ...mockBacktestResult.performance,
        totalReturn: -5.5,
        annualizedReturn: -5.5,
      },
    };

    render(<BacktestChart result={negativeResult} />);

    expect(screen.getByText("-5.50%")).toBeInTheDocument();
  });

  it("应该处理极值数据", () => {
    const extremeResult = {
      ...mockBacktestResult,
      performance: {
        ...mockBacktestResult.performance,
        totalReturn: 999.99,
        volatility: 0.01,
        sharpeRatio: 99.99,
        maxDrawdown: 99.99,
      },
    };

    render(<BacktestChart result={extremeResult} />);

    expect(screen.getByText("999.99%")).toBeInTheDocument();
    expect(screen.getByText("0.01%")).toBeInTheDocument();
    expect(screen.getByText("99.99")).toBeInTheDocument();
  });
});
